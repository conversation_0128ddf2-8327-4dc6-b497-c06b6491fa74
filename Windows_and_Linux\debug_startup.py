#!/usr/bin/env python3
"""
Debug script to test the startup logic and identify the systray issue.
"""

import sys
import os
import logging

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Settings<PERSON>anager

def test_settings_loading():
    """Test the settings loading and provider configuration detection."""
    print("=== Testing Settings Loading ===")
    
    # Test with build-dev mode (like the executable)
    mode = "build-dev"
    print(f"Testing with mode: {mode}")
    
    try:
        settings_manager = SettingsManager(mode=mode)
        print(f"✓ SettingsManager created successfully")
        print(f"  Mode: {settings_manager.mode}")
        print(f"  Data file: {settings_manager.data_file}")
        print(f"  Data file exists: {settings_manager.data_file.exists()}")
        
        # Load settings
        settings = settings_manager.load_settings()
        print(f"✓ Settings loaded successfully")
        
        # Test has_providers_configured
        has_providers = settings_manager.has_providers_configured()
        print(f"  has_providers_configured(): {has_providers}")
        
        # Debug the provider check logic
        if settings.custom_data:
            providers = settings.custom_data.get("providers", {})
            print(f"  custom_data.providers: {providers}")
            print(f"  providers count: {len(providers) if providers else 0}")
        else:
            print(f"  custom_data: None")
            
        # Check system settings
        system = settings.system
        print(f"  system.provider: '{system.provider}'")
        print(f"  system.api_key: '{system.api_key}' (length: {len(system.api_key) if system.api_key else 0})")
        
        return has_providers
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("Writing Tools Startup Debug Script")
    print("=" * 50)
    
    # Set up logging
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Test settings loading
    has_providers = test_settings_loading()
    
    print("\n=== Summary ===")
    if has_providers:
        print("✓ Providers are configured - app should create tray icon")
    else:
        print("❌ No providers configured - app should show onboarding")
    
    print("\nThis script helps debug why the app might not be starting correctly.")

if __name__ == "__main__":
    main()
