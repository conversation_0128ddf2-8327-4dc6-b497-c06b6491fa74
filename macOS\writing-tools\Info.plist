<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleIdentifier</key>
	<string>com.aryamirsepasi.writing-tools</string>
	<key>CFBundleName</key>
	<string>WritingTools</string>
	<key>CFBundleDisplayName</key>
	<string>WritingTools</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
    <key>CFBundleShortVersionString</key>
    <string>$(MARKETING_VERSION)</string>
    <key>CFBundleVersion</key>
    <string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSMinimumSystemVersion</key>
	<string>$(MACOSX_DEPLOYMENT_TARGET)</string>
	<key>LSUIElement</key>
	<true/>
	<key>NSServices</key>
	<array/>
	<key>NSAccessibilityUsageDescription</key>
<string>WritingTools needs access to control your computer to simulate copy and paste actions, allowing it to retrieve selected text and insert AI-generated results.</string>
</dict>
</plist>
