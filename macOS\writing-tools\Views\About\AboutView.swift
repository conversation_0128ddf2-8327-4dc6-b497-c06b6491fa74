import SwiftUI

struct AboutView: View {
    @ObservedObject private var settings = AppSettings.shared
    @State private var updateChecker = UpdateChecker.shared
    
    var body: some View {
        VStack(spacing: 20) {
            Text("About Writing Tools")
                .font(.largeTitle)
                .bold()
            
            Text("Writing Tools is a free & lightweight tool that helps you improve your writing with AI, similar to Apple's new Apple Intelligence feature.")
                .multilineTextAlignment(.center)
            
            VStack(spacing: 10) {
                Text("Created with care by <PERSON><PERSON>, a high school student.")
                    .bold()
                
                Link("Email: <EMAIL>",
                     destination: URL(string: "mailto:<EMAIL>")!)
                
                Link("Check out Bliss AI on Google Play",
                     destination: URL(string: "https://play.google.com/store/apps/details?id=com.jesai.blissai")!)
            }
            
            Divider()
            
            VStack(spacing: 10) {
                Text("The macOS version is created by Arya Mirsepasi")
                    .bold()
                
                Link("Email: <EMAIL>",
                     destination: URL(string: "mailto:<EMAIL>")!)
                
                Link("Check out ProseKey AI (iOS port of WritingTools)",
                     destination: URL(string: "https://apps.apple.com/us/app/prosekey-ai/id6741180175")!)
            }
            
            Divider()
            
            Text("Version: 4.2 (Based on Windows Port version 7.1)")
                .font(.caption)
            
            // Update checker section
            VStack(spacing: 8) {
                if updateChecker.isCheckingForUpdates {
                    ProgressView("Checking for updates...")
                } else if let error = updateChecker.checkError {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.caption)
                } else if updateChecker.updateAvailable {
                    Text("A new version is available!")
                        .foregroundColor(.green)
                        .font(.caption)
                } else if !updateChecker.updateAvailable {
                    Text("The latest version is already installed!")
                        .foregroundColor(.green)
                        .font(.caption)
                }
                
                Button(action: {
                    if updateChecker.updateAvailable {
                        updateChecker.openReleasesPage()
                    } else {
                        Task {
                            await updateChecker.checkForUpdates()
                        }
                    }
                }) {
                    Text(updateChecker.updateAvailable ? "Download Update" : "Check for Updates")
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .padding()
        .frame(width: 400, height: 400)
        .windowBackground(useGradient: settings.useGradientTheme)
    }
}
