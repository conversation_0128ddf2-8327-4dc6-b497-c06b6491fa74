# Italian translations for PACKAGE package.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#  <<EMAIL>>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-28 19:44+0100\n"
"PO-Revision-Date: 2025-01-27 22:14+0100\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Italian <<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.5\n"

#: ui/AboutWindow.py:44
msgid "About Writing Tools"
msgstr ""

#: ui/AboutWindow.py:49
msgid ""
"Writing Tools is a free & lightweight tool that helps you improve your "
"writing with AI, similar to Apple's new Apple Intelligence feature. It works "
"with an extensive range of AI LLMs, both online and locally run."
msgstr ""

#: ui/AboutWindow.py:54
msgid "Created with care by Jesai, a high school student."
msgstr ""

#: ui/AboutWindow.py:55
msgid "Feel free to check out my other AI app"
msgstr ""

#: ui/AboutWindow.py:55
msgid "It's a novel AI tutor that's free on the Google Play Store :)"
msgstr ""

#: ui/AboutWindow.py:56
msgid "Contact me"
msgstr ""

#: ui/AboutWindow.py:60
msgid ""
"Writing Tools would not be where it is today without its <u>amazing</u> "
"contributors"
msgstr ""

#: ui/AboutWindow.py:62
msgid ""
"Extensively refactored Writing Tools and added OpenAI Compatible API "
"support, streamed responses, and the text generation mode when no text is "
"selected."
msgstr ""

#: ui/AboutWindow.py:64
msgid ""
"Added Linux support, switched to the pynput API to improve Windows "
"stability. Added Ollama API support, custom options and localization. Fixed "
"misc. bugs and added graceful termination support by handling SIGINT signal."
msgstr ""

#: ui/AboutWindow.py:66
msgid ""
"Helped add dark mode, the plain theme, tray menu fixes, and UI improvements."
msgstr ""

#: ui/AboutWindow.py:68
msgid "Helped improve the reliability of text selection."
msgstr ""

#: ui/AboutWindow.py:70
msgid "Made the rounded corners anti-aliased & prettier."
msgstr ""

#: ui/AboutWindow.py:72
msgid ""
"Significantly improved the About window, making it scrollable and cleaning "
"things up. Also improved our .gitignore & requirements.txt."
msgstr ""

#: ui/AboutWindow.py:74
msgid "Helped add the start-on-boot setting."
msgstr ""

#: ui/CustomPopupWindow.py:75 ui/CustomPopupWindow.py:74
msgid "Describe your change..."
msgstr ""

#: ui/CustomPopupWindow.py:75 ui/CustomPopupWindow.py:74
msgid "Ask your AI..."
msgstr ""

#: ui/CustomPopupWindow.py:109
msgid "Proofread"
msgstr ""

#: ui/CustomPopupWindow.py:110
msgid "Rewrite"
msgstr ""

#: ui/CustomPopupWindow.py:111
msgid "Friendly"
msgstr ""

#: ui/CustomPopupWindow.py:112
msgid "Professional"
msgstr ""

#: ui/CustomPopupWindow.py:113
msgid "Concise"
msgstr ""

#: ui/CustomPopupWindow.py:114
msgid "Summary"
msgstr ""

#: ui/CustomPopupWindow.py:115
msgid "Key Points"
msgstr ""

#: ui/CustomPopupWindow.py:116
msgid "Table"
msgstr ""

#: ui/OnboardingWindow.py:24 ui/OnboardingWindow.py:40
#: ui/OnboardingWindow.py:26 ui/OnboardingWindow.py:42
msgid "Welcome to Writing Tools"
msgstr ""

#: ui/OnboardingWindow.py:45 ui/OnboardingWindow.py:47
msgid ""
"Instantly optimize your writing with AI by selecting your text and invoking "
"Writing Tools with \"ctrl+space\", anywhere."
msgstr ""

#: ui/OnboardingWindow.py:47 ui/OnboardingWindow.py:49
msgid ""
"Get a summary you can chat with of articles, YouTube videos, or documents by "
"select all text with \"ctrl+a\""
msgstr ""

#: ui/OnboardingWindow.py:48 ui/OnboardingWindow.py:50
msgid ""
"(or select the YouTube transcript from its description), invoking Writing "
"Tools, and choosing Summary."
msgstr ""

#: ui/OnboardingWindow.py:50 ui/OnboardingWindow.py:52
msgid ""
"Chat with AI anytime by invoking Writing Tools without selecting any text."
msgstr ""

#: ui/OnboardingWindow.py:52 ui/OnboardingWindow.py:54
msgid "Supports an extensive range of AI models:"
msgstr ""

#: ui/OnboardingWindow.py:53 ui/OnboardingWindow.py:55
msgid "Gemini 2.0"
msgstr ""

#: ui/OnboardingWindow.py:54 ui/OnboardingWindow.py:56
msgid "ANY OpenAI Compatible API — including local LLMs!"
msgstr ""

#: ui/OnboardingWindow.py:75 ui/OnboardingWindow.py:77
msgid "Choose your theme:"
msgstr ""

#: ui/OnboardingWindow.py:80 ui/OnboardingWindow.py:82
msgid "Gradient"
msgstr ""

#: ui/OnboardingWindow.py:81 ui/SettingsWindow.py:239 ui/OnboardingWindow.py:83
#: ui/SettingsWindow.py:244
msgid "Plain"
msgstr ""

#: ui/OnboardingWindow.py:90 ui/OnboardingWindow.py:92
msgid "Next"
msgstr ""

#: ui/ResponseWindow.py:301 ui/ResponseWindow.py:303
msgid "Response"
msgstr ""

#: ui/ResponseWindow.py:379 ui/ResponseWindow.py:385
msgid "Select to copy with formatting"
msgstr ""

#: ui/ResponseWindow.py:384 ui/ResponseWindow.py:390
msgid "Copy as Markdown"
msgstr ""

#: ui/ResponseWindow.py:395 ui/ResponseWindow.py:507 ui/ResponseWindow.py:509
#: ui/ResponseWindow.py:516 ui/ResponseWindow.py:520 ui/ResponseWindow.py:401
#: ui/ResponseWindow.py:513 ui/ResponseWindow.py:515 ui/ResponseWindow.py:522
#: ui/ResponseWindow.py:526
msgid "Thinking"
msgstr ""

#: ui/ResponseWindow.py:429 ui/ResponseWindow.py:530 ui/ResponseWindow.py:435
#: ui/ResponseWindow.py:536
msgid "Ask a follow-up question"
msgstr ""

#: ui/SettingsWindow.py:32 ui/SettingsWindow.py:151 ui/SettingsWindow.py:205
#: WritingToolApp.py:568 ui/SettingsWindow.py:37 ui/SettingsWindow.py:156
#: ui/SettingsWindow.py:210 WritingToolApp.py:569
msgid "Settings"
msgstr ""

#: ui/SettingsWindow.py:211 ui/SettingsWindow.py:216
msgid "Start on Boot"
msgstr ""

#: ui/SettingsWindow.py:218 ui/SettingsWindow.py:223
msgid "Shortcut Key:"
msgstr ""

#: ui/SettingsWindow.py:233 ui/SettingsWindow.py:238
msgid "Background Theme:"
msgstr ""

#: ui/SettingsWindow.py:238 ui/SettingsWindow.py:243
msgid "Blurry Gradient"
msgstr ""

#: ui/SettingsWindow.py:250 ui/SettingsWindow.py:255
msgid "Choose AI Provider:"
msgstr ""

#: ui/SettingsWindow.py:307 ui/SettingsWindow.py:312
msgid "Finish AI Setup"
msgstr ""

#: ui/SettingsWindow.py:307 ui/SettingsWindow.py:312
msgid "Save"
msgstr ""

#: ui/SettingsWindow.py:326 ui/SettingsWindow.py:331
msgid "Please restart Writing Tools for changes to take effect."
msgstr ""

#: WritingToolApp.py:571 WritingToolApp.py:572
msgid "About"
msgstr ""

#: WritingToolApp.py:574 WritingToolApp.py:575
msgid "Exit"
msgstr ""
