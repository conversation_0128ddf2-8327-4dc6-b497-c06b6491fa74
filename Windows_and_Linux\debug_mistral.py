#!/usr/bin/env python3
"""
Debug script to test Mistral configuration and API calls.
"""

import sys
import os
import logging
import requests

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import SettingsManager
from config.constants import MISTRAL_MODELS

def test_mistral_api_direct():
    """Test Mistral API directly like the working script."""
    print("=== Testing Mistral API Directly ===")
    
    # Use the API key from the working script
    api_key = "Bi4aRaP9DdYvsWxfDTqTBF21MSR5f3LI"
    
    url = "https://api.mistral.ai/v1/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "mistral-small-latest",  # Use a current model
        "messages": [
            {"role": "user", "content": "Say hello in English!"}
        ],
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    try:
        print(f"Making request to {url}")
        print(f"Model: {data['model']}")
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                print(f"✓ Response: {content}")
                return True
            else:
                print(f"❌ No content in response: {result}")
                return False
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_mistral_config():
    """Test Mistral configuration in the app."""
    print("\n=== Testing Mistral Configuration ===")
    
    try:
        # Test with build-dev mode (like the executable)
        settings_manager = SettingsManager(mode="build-dev")
        settings = settings_manager.load_settings()
        
        print(f"Settings loaded successfully")
        
        # Check Mistral configuration
        system = settings.system
        print(f"System mistral_model: '{system.mistral_model}'")
        print(f"System mistral_base_url: '{system.mistral_base_url}'")
        
        # Check custom_data
        custom_data = settings.custom_data
        providers_data = custom_data.get("providers", {})
        mistral_config = providers_data.get("Mistral AI", {})
        
        print(f"Custom Mistral config: {mistral_config}")
        
        # Test _get_provider_config logic manually
        saved_config = mistral_config
        config = {
            "api_key": "",  # Will be overridden by saved_config
            "api_model": system.mistral_model,
        }
        config.update(saved_config)
        
        print(f"Final config: {config}")
        
        # Check if config is valid
        if not config.get("api_key"):
            print("❌ No API key configured")
        if not config.get("api_model"):
            print("❌ No model configured")
            
        return config
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_mistral_models():
    """Test available Mistral models."""
    print("\n=== Available Mistral Models ===")
    
    for display_name, model_id in MISTRAL_MODELS:
        print(f"  {display_name}: {model_id}")

def suggest_fix():
    """Suggest how to fix the Mistral configuration."""
    print("\n=== Suggested Fix ===")
    print("The problem is that Mistral is not properly configured in the app.")
    print("To fix this, you need to:")
    print("1. Open the app settings")
    print("2. Select Mistral AI as provider")
    print("3. Enter your API key: Bi4aRaP9DdYvsWxfDTqTBF21MSR5f3LI")
    print("4. Select a model (e.g., mistral-small-latest)")
    print("\nAlternatively, we can update the data.json file directly.")

def main():
    """Main function."""
    print("Mistral Debug Script")
    print("=" * 50)
    
    # Set up logging
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Test direct API call
    api_works = test_mistral_api_direct()
    
    # Test app configuration
    config = test_mistral_config()
    
    # Show available models
    test_mistral_models()
    
    # Suggest fix
    suggest_fix()
    
    print("\n=== Summary ===")
    if api_works:
        print("✓ Mistral API works directly")
    else:
        print("❌ Mistral API failed directly")
        
    if config and config.get("api_key") and config.get("api_model"):
        print("✓ App configuration looks good")
    else:
        print("❌ App configuration is incomplete")

if __name__ == "__main__":
    main()
