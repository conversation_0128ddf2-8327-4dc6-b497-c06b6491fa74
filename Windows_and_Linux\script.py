import requests
import json
import logging
from typing import Optional, List, Dict

# Configuration du logging pour voir tous les détails
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

class MistralDebugger:
    """Classe pour déboguer les appels à l'API Mistral"""
    
    def __init__(self, api_key: str, model: str = "mistral-tiny"):
        self.api_key = api_key
        self.model = model
        self.base_url = "https://api.mistral.ai/v1/chat/completions"
        
    def test_basic_request(self):
        """Test basique de l'API Mistral"""
        print("\n=== TEST 1: Requête basique ===")
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        
        data = {
            "model": self.model,
            "messages": [
                {"role": "user", "content": "Dis simplement 'Bonjour'"}
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        print(f"URL: {self.base_url}")
        print(f"Headers: {headers}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            print(f"\nStatus Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Response JSON: {json.dumps(result, indent=2)}")
                
                if "choices" in result and len(result["choices"]) > 0:
                    content = result["choices"][0]["message"]["content"]
                    print(f"\n✅ Réponse reçue: {content}")
                    return True
                else:
                    print("❌ Pas de 'choices' dans la réponse")
                    return False
            else:
                print(f"❌ Erreur HTTP: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Exception: {type(e).__name__}: {str(e)}")
            return False
    
    def test_with_system_message(self):
        """Test avec un message système"""
        print("\n=== TEST 2: Avec message système ===")
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        
        messages = [
            {"role": "system", "content": "Tu es un assistant utile."},
            {"role": "user", "content": "Quelle est la capitale de la France?"}
        ]
        
        data = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 4000
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    content = result["choices"][0]["message"]["content"]
                    print(f"✅ Réponse: {content[:100]}...")
                    return True
            else:
                print(f"❌ Erreur: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
            return False
    
    def simulate_class_behavior(self, system_instruction: str, prompt: str, 
                              conversation_history: Optional[List[Dict]] = None,
                              return_response: bool = False):
        """Simule le comportement exact de la méthode get_response"""
        print(f"\n=== TEST 3: Simulation du comportement de la classe ===")
        print(f"return_response: {return_response}")
        
        try:
            url = "https://api.mistral.ai/v1/chat/completions"
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }
            
            messages = []
            
            if system_instruction:
                messages.append({"role": "system", "content": system_instruction})
                
            if conversation_history:
                messages.extend(conversation_history)
                
            messages.append({"role": "user", "content": prompt})
            
            data = {
                "model": self.model,
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 4000,
            }
            
            print(f"Messages envoyés: {json.dumps(messages, indent=2)}")
            
            response = requests.post(url, headers=headers, json=data, timeout=60)
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if "choices" in result and len(result["choices"]) > 0:
                    response_text = result["choices"][0]["message"]["content"]
                    
                    print(f"Longueur de la réponse: {len(response_text) if response_text else 0}")
                    
                    if not response_text or response_text.strip() == "":
                        print("❌ Réponse vide!")
                        return ""
                    
                    print(f"✅ Réponse: {response_text[:200]}...")
                    
                    if return_response:
                        return response_text
                    else:
                        # Simule l'émission du signal
                        print(f"Signal émis avec: {response_text[:100]}...")
                        return response_text
                else:
                    print("❌ Pas de contenu dans la réponse")
                    return ""
            else:
                print(f"❌ Erreur API: {response.text}")
                return ""
                
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
            import traceback
            traceback.print_exc()
            return ""

# Fonction principale pour tester
def main():
    print("=== DÉBOGAGE API MISTRAL ===\n")
    
    # IMPORTANT: Remplacez par votre clé API
    API_KEY = "Bi4aRaP9DdYvsWxfDTqTBF21MSR5f3LI"
    # <-- Mettez votre clé API ici
    MODEL = "open-mistral-7b"  # ou le modèle que vous utilisez
    
    if API_KEY == "VOTRE_CLE_API_ICI":
        print("⚠️  ATTENTION: Vous devez remplacer API_KEY par votre vraie clé API Mistral!")
        print("Obtenez une clé sur: https://console.mistral.ai/api-keys")
        return
    
    debugger = MistralDebugger(API_KEY, MODEL)
    
    # Test 1: Requête basique
    success1 = debugger.test_basic_request()
    
    # Test 2: Avec message système
    success2 = debugger.test_with_system_message()
    
    # Test 3: Simulation exacte du comportement de la classe
    result3 = debugger.simulate_class_behavior(
        system_instruction="Tu es un assistant utile.",
        prompt="Écris un court poème sur Python.",
        return_response=True
    )
    
    # Test 4: Sans return_response (comme dans l'utilisation normale)
    result4 = debugger.simulate_class_behavior(
        system_instruction="Tu es un assistant utile.",
        prompt="Quelle est la capitale du Japon?",
        return_response=False
    )
    
    print("\n=== RÉSUMÉ DES TESTS ===")
    print(f"Test 1 (basique): {'✅ Succès' if success1 else '❌ Échec'}")
    print(f"Test 2 (avec système): {'✅ Succès' if success2 else '❌ Échec'}")
    print(f"Test 3 (simulation avec return): {'✅ Succès' if result3 else '❌ Échec'}")
    print(f"Test 4 (simulation sans return): {'✅ Succès' if result4 else '❌ Échec'}")
    
    print("\n=== POINTS À VÉRIFIER ===")
    print("1. Vérifiez que votre clé API est valide")
    print("2. Vérifiez que le modèle sélectionné existe (mistral-tiny, mistral-small, etc.)")
    print("3. Vérifiez votre connexion internet")
    print("4. Vérifiez que self.app.output_ready_signal.emit() fonctionne correctement")
    print("5. Vérifiez les logs de votre application pour voir si le signal est bien reçu")

if __name__ == "__main__":
    main()

# Pour tester des cas spécifiques, décommentez les lignes suivantes:

# Test avec une mauvaise clé API
# bad_debugger = MistralDebugger("invalid_key", "mistral-tiny")
# bad_debugger.test_basic_request()

# Test avec un modèle inexistant
# wrong_model = MistralDebugger("your_api_key", "model-inexistant")
# wrong_model.test_basic_request()