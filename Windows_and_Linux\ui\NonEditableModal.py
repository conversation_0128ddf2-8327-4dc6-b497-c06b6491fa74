import logging
from PySide6 import Qt<PERSON>ore, QtGui, QtWidgets
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QTextEdit,
    QPushButton,
    QLabel,
)


class NonEditableModal(QDialog):
    """
    Modal window that displays transformed text when the original text is in a non-editable field.
    Provides action buttons for common operations like Proofread, Rewrite, etc.
    """

    # Signal emitted when user wants to perform an action on the text
    action_requested = Signal(str, str)  # action_name, text

    def __init__(self, parent, transformed_text, original_text):
        super().__init__(parent)
        self.transformed_text = transformed_text
        self.original_text = original_text
        self.parent_app = parent

        self.setWindowTitle("Writing Tools - Non-editable Text")
        self.setModal(True)
        self.resize(600, 500)

        # Set window flags to keep it on top
        self.setWindowFlags(Qt.Dialog | Qt.WindowStaysOnTopHint)

        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # Title label
        title_label = QLabel("Text transformed successfully!")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(
            "font-size: 16px; font-weight: bold; margin-bottom: 10px;"
        )
        layout.addWidget(title_label)

        # Info label
        info_label = QLabel(
            "The text below has been transformed. You can copy it or perform additional actions:"
        )
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # Text display area
        self.text_display = QTextEdit()
        self.text_display.setPlainText(self.transformed_text)
        self.text_display.setReadOnly(True)
        self.text_display.setMinimumHeight(200)
        layout.addWidget(self.text_display)

        # Copy button
        copy_layout = QHBoxLayout()
        copy_layout.addStretch()

        self.copy_button = QPushButton("📋 Copy to Clipboard")
        self.copy_button.clicked.connect(self.copy_to_clipboard)
        self.copy_button.setMinimumHeight(35)
        copy_layout.addWidget(self.copy_button)

        copy_layout.addStretch()
        layout.addLayout(copy_layout)

        # Action buttons section
        actions_label = QLabel("Perform additional actions:")
        actions_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(actions_label)

        # Create action buttons grid
        actions_layout = QVBoxLayout()

        # First row
        row1 = QHBoxLayout()
        self.create_action_button("✏️ Proofread", "Proofread", row1)
        self.create_action_button("🔄 Rewrite", "Rewrite", row1)
        actions_layout.addLayout(row1)

        # Second row
        row2 = QHBoxLayout()
        self.create_action_button("😊 Friendly", "Friendly", row2)
        self.create_action_button("💼 Professional", "Professional", row2)
        actions_layout.addLayout(row2)

        # Third row
        row3 = QHBoxLayout()
        self.create_action_button("📝 Concise", "Concise", row3)
        self.create_action_button("📊 Table", "Table", row3)
        actions_layout.addLayout(row3)

        # Fourth row
        row4 = QHBoxLayout()
        self.create_action_button("🔑 Key Points", "Key Points", row4)
        self.create_action_button("📋 Summary", "Summary", row4)
        actions_layout.addLayout(row4)

        layout.addLayout(actions_layout)

        # Close button
        close_layout = QHBoxLayout()
        close_layout.addStretch()

        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        close_button.setMinimumHeight(35)
        close_layout.addWidget(close_button)

        close_layout.addStretch()
        layout.addLayout(close_layout)

    def create_action_button(self, text, action, layout):
        """Create an action button and add it to the layout"""
        button = QPushButton(text)
        button.setMinimumHeight(40)
        button.setMinimumWidth(120)
        button.clicked.connect(lambda: self.perform_action(action))
        layout.addWidget(button)

    def copy_to_clipboard(self):
        """Copy the transformed text to clipboard"""
        try:
            import pyperclip

            pyperclip.copy(self.transformed_text)
            # Temporarily change button text to show success
            original_text = self.copy_button.text()
            self.copy_button.setText("✅ Copied!")
            self.copy_button.setEnabled(False)

            # Reset button after 1.5 seconds
            QtCore.QTimer.singleShot(
                1500, lambda: self.reset_copy_button(original_text)
            )

        except Exception as e:
            logging.error(f"Error copying to clipboard: {e}")

    def reset_copy_button(self, original_text):
        """Reset the copy button to its original state"""
        self.copy_button.setText(original_text)
        self.copy_button.setEnabled(True)

    def perform_action(self, action):
        """Perform the requested action on the current text"""
        try:
            # Close this modal
            self.close()

            # Use the current transformed text as input for the new action
            current_text = self.text_display.toPlainText()

            # Trigger the action through the parent app
            if hasattr(self.parent_app, "process_text_with_option"):
                self.parent_app.process_text_with_option(action, current_text)
            else:
                logging.warning(
                    f"Parent app doesn't have process_text_with_option method"
                )

        except Exception as e:
            logging.error(f"Error performing action {action}: {e}")

    def apply_styles(self):
        """Apply styling to the modal"""
        # Apply dark theme if needed
        try:
            import darkdetect

            if darkdetect.isDark():
                self.setStyleSheet(
                    """
                    QDialog {
                        background-color: #2b2b2b;
                        color: #ffffff;
                    }
                    QTextEdit {
                        background-color: #3c3c3c;
                        border: 1px solid #555555;
                        border-radius: 5px;
                        padding: 10px;
                        color: #ffffff;
                    }
                    QPushButton {
                        background-color: #4a4a4a;
                        border: 1px solid #666666;
                        border-radius: 5px;
                        padding: 8px;
                        color: #ffffff;
                    }
                    QPushButton:hover {
                        background-color: #5a5a5a;
                    }
                    QPushButton:pressed {
                        background-color: #3a3a3a;
                    }
                    QPushButton:disabled {
                        background-color: #2a2a2a;
                        color: #888888;
                    }
                    QLabel {
                        color: #ffffff;
                    }
                """
                )
        except ImportError:
            # If darkdetect is not available, use light theme
            pass
