// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		2A999C902D84D81A00DFB0A6 /* KeyboardShortcuts in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999C8F2D84D81A00DFB0A6 /* KeyboardShortcuts */; };
		2A999C9E2D84D88000DFB0A6 /* AIProxy in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999C9D2D84D88000DFB0A6 /* AIProxy */; };
		2A999CA12D84D89700DFB0A6 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999CA02D84D89700DFB0A6 /* MarkdownUI */; };
		2A999CA42D84D91700DFB0A6 /* MLX in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999CA32D84D91700DFB0A6 /* MLX */; };
		2A999CA62D84D91800DFB0A6 /* MLXFFT in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999CA52D84D91800DFB0A6 /* MLXFFT */; };
		2A999CA82D84D91800DFB0A6 /* MLXFast in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999CA72D84D91800DFB0A6 /* MLXFast */; };
		2A999CAA2D84D91800DFB0A6 /* MLXLinalg in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999CA92D84D91800DFB0A6 /* MLXLinalg */; };
		2A999CAC2D84D91800DFB0A6 /* MLXNN in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999CAB2D84D91800DFB0A6 /* MLXNN */; };
		2A999CAF2D84D9B800DFB0A6 /* MLXEmbedders in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999CAE2D84D9B800DFB0A6 /* MLXEmbedders */; };
		2A999CB12D84D9B800DFB0A6 /* MLXLLM in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999CB02D84D9B800DFB0A6 /* MLXLLM */; };
		2A999CB32D84D9B800DFB0A6 /* MLXLMCommon in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999CB22D84D9B800DFB0A6 /* MLXLMCommon */; };
		2A999CB52D84D9B800DFB0A6 /* MLXMNIST in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999CB42D84D9B800DFB0A6 /* MLXMNIST */; };
		2A999CB72D84D9B800DFB0A6 /* MLXVLM in Frameworks */ = {isa = PBXBuildFile; productRef = 2A999CB62D84D9B800DFB0A6 /* MLXVLM */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		2ABCBC1E2CDEB606001E4B5E /* writing-tools.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "writing-tools.app"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		2A71341F2CF9D78E005ECCA8 /* Exceptions for "writing-tools" folder in "writing-tools" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 2ABCBC1D2CDEB606001E4B5E /* writing-tools */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		2ABCBC202CDEB606001E4B5E /* writing-tools */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				2A71341F2CF9D78E005ECCA8 /* Exceptions for "writing-tools" folder in "writing-tools" target */,
			);
			path = "writing-tools";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		2ABCBC1B2CDEB606001E4B5E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2A999CB72D84D9B800DFB0A6 /* MLXVLM in Frameworks */,
				2A999CB52D84D9B800DFB0A6 /* MLXMNIST in Frameworks */,
				2A999CB12D84D9B800DFB0A6 /* MLXLLM in Frameworks */,
				2A999CA82D84D91800DFB0A6 /* MLXFast in Frameworks */,
				2A999CAA2D84D91800DFB0A6 /* MLXLinalg in Frameworks */,
				2A999CB32D84D9B800DFB0A6 /* MLXLMCommon in Frameworks */,
				2A999CAF2D84D9B800DFB0A6 /* MLXEmbedders in Frameworks */,
				2A999CAC2D84D91800DFB0A6 /* MLXNN in Frameworks */,
				2A999CA62D84D91800DFB0A6 /* MLXFFT in Frameworks */,
				2A999CA42D84D91700DFB0A6 /* MLX in Frameworks */,
				2A999C9E2D84D88000DFB0A6 /* AIProxy in Frameworks */,
				2A999CA12D84D89700DFB0A6 /* MarkdownUI in Frameworks */,
				2A999C902D84D81A00DFB0A6 /* KeyboardShortcuts in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2ABCBC152CDEB606001E4B5E = {
			isa = PBXGroup;
			children = (
				2ABCBC202CDEB606001E4B5E /* writing-tools */,
				2ABCBC1F2CDEB606001E4B5E /* Products */,
			);
			sourceTree = "<group>";
		};
		2ABCBC1F2CDEB606001E4B5E /* Products */ = {
			isa = PBXGroup;
			children = (
				2ABCBC1E2CDEB606001E4B5E /* writing-tools.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2ABCBC1D2CDEB606001E4B5E /* writing-tools */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2ABCBC432CDEB607001E4B5E /* Build configuration list for PBXNativeTarget "writing-tools" */;
			buildPhases = (
				2ABCBC1A2CDEB606001E4B5E /* Sources */,
				2ABCBC1B2CDEB606001E4B5E /* Frameworks */,
				2ABCBC1C2CDEB606001E4B5E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				2ABCBC202CDEB606001E4B5E /* writing-tools */,
			);
			name = "writing-tools";
			packageProductDependencies = (
				2A999C8F2D84D81A00DFB0A6 /* KeyboardShortcuts */,
				2A999C9D2D84D88000DFB0A6 /* AIProxy */,
				2A999CA02D84D89700DFB0A6 /* MarkdownUI */,
				2A999CA32D84D91700DFB0A6 /* MLX */,
				2A999CA52D84D91800DFB0A6 /* MLXFFT */,
				2A999CA72D84D91800DFB0A6 /* MLXFast */,
				2A999CA92D84D91800DFB0A6 /* MLXLinalg */,
				2A999CAB2D84D91800DFB0A6 /* MLXNN */,
				2A999CAE2D84D9B800DFB0A6 /* MLXEmbedders */,
				2A999CB02D84D9B800DFB0A6 /* MLXLLM */,
				2A999CB22D84D9B800DFB0A6 /* MLXLMCommon */,
				2A999CB42D84D9B800DFB0A6 /* MLXMNIST */,
				2A999CB62D84D9B800DFB0A6 /* MLXVLM */,
			);
			productName = "swift-writing-tools";
			productReference = 2ABCBC1E2CDEB606001E4B5E /* writing-tools.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2ABCBC162CDEB606001E4B5E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					2ABCBC1D2CDEB606001E4B5E = {
						CreatedOnToolsVersion = 16.1;
					};
				};
			};
			buildConfigurationList = 2ABCBC192CDEB606001E4B5E /* Build configuration list for PBXProject "writing-tools" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				de,
				fr,
				es,
			);
			mainGroup = 2ABCBC152CDEB606001E4B5E;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				2A999C8E2D84D81A00DFB0A6 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */,
				2A999C9C2D84D88000DFB0A6 /* XCRemoteSwiftPackageReference "AIProxySwift" */,
				2A999C9F2D84D89700DFB0A6 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */,
				2A999CA22D84D91700DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift" */,
				2A999CAD2D84D9B800DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift-examples" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 2ABCBC1F2CDEB606001E4B5E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2ABCBC1D2CDEB606001E4B5E /* writing-tools */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2ABCBC1C2CDEB606001E4B5E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2ABCBC1A2CDEB606001E4B5E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		2ABCBC412CDEB607001E4B5E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = MK2V998W66;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.1;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2ABCBC422CDEB607001E4B5E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = MK2V998W66;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.1;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
			};
			name = Release;
		};
		2ABCBC442CDEB607001E4B5E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "writing-tools/writing_tools.entitlements";
				CODE_SIGN_IDENTITY = "Developer ID Application";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Developer ID Application";
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 14;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"writing-tools/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=macosx*]" = MK2V998W66;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "writing-tools/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "WritingTools ";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 4.2;
				PRODUCT_BUNDLE_IDENTIFIER = "com.aryamirsepasi.writing-tools";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		2ABCBC452CDEB607001E4B5E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "writing-tools/writing_tools.entitlements";
				CODE_SIGN_IDENTITY = "Developer ID Application";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Developer ID Application";
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 14;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"writing-tools/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=macosx*]" = MK2V998W66;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "writing-tools/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "WritingTools ";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 4.2;
				PRODUCT_BUNDLE_IDENTIFIER = "com.aryamirsepasi.writing-tools";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2ABCBC192CDEB606001E4B5E /* Build configuration list for PBXProject "writing-tools" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2ABCBC412CDEB607001E4B5E /* Debug */,
				2ABCBC422CDEB607001E4B5E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2ABCBC432CDEB607001E4B5E /* Build configuration list for PBXNativeTarget "writing-tools" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2ABCBC442CDEB607001E4B5E /* Debug */,
				2ABCBC452CDEB607001E4B5E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		2A999C8E2D84D81A00DFB0A6 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sindresorhus/KeyboardShortcuts";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.3.0;
			};
		};
		2A999C9C2D84D88000DFB0A6 /* XCRemoteSwiftPackageReference "AIProxySwift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/lzell/AIProxySwift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.75.2;
			};
		};
		2A999C9F2D84D89700DFB0A6 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/gonzalezreal/swift-markdown-ui";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.1;
			};
		};
		2A999CA22D84D91700DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/ml-explore/mlx-swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.21.3;
			};
		};
		2A999CAD2D84D9B800DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift-examples" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/ml-explore/mlx-swift-examples";
			requirement = {
				branch = main;
				kind = branch;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		2A999C8F2D84D81A00DFB0A6 /* KeyboardShortcuts */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999C8E2D84D81A00DFB0A6 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */;
			productName = KeyboardShortcuts;
		};
		2A999C9D2D84D88000DFB0A6 /* AIProxy */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999C9C2D84D88000DFB0A6 /* XCRemoteSwiftPackageReference "AIProxySwift" */;
			productName = AIProxy;
		};
		2A999CA02D84D89700DFB0A6 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999C9F2D84D89700DFB0A6 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */;
			productName = MarkdownUI;
		};
		2A999CA32D84D91700DFB0A6 /* MLX */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999CA22D84D91700DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift" */;
			productName = MLX;
		};
		2A999CA52D84D91800DFB0A6 /* MLXFFT */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999CA22D84D91700DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift" */;
			productName = MLXFFT;
		};
		2A999CA72D84D91800DFB0A6 /* MLXFast */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999CA22D84D91700DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift" */;
			productName = MLXFast;
		};
		2A999CA92D84D91800DFB0A6 /* MLXLinalg */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999CA22D84D91700DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift" */;
			productName = MLXLinalg;
		};
		2A999CAB2D84D91800DFB0A6 /* MLXNN */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999CA22D84D91700DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift" */;
			productName = MLXNN;
		};
		2A999CAE2D84D9B800DFB0A6 /* MLXEmbedders */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999CAD2D84D9B800DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift-examples" */;
			productName = MLXEmbedders;
		};
		2A999CB02D84D9B800DFB0A6 /* MLXLLM */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999CAD2D84D9B800DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift-examples" */;
			productName = MLXLLM;
		};
		2A999CB22D84D9B800DFB0A6 /* MLXLMCommon */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999CAD2D84D9B800DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift-examples" */;
			productName = MLXLMCommon;
		};
		2A999CB42D84D9B800DFB0A6 /* MLXMNIST */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999CAD2D84D9B800DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift-examples" */;
			productName = MLXMNIST;
		};
		2A999CB62D84D9B800DFB0A6 /* MLXVLM */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A999CAD2D84D9B800DFB0A6 /* XCRemoteSwiftPackageReference "mlx-swift-examples" */;
			productName = MLXVLM;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 2ABCBC162CDEB606001E4B5E /* Project object */;
}
